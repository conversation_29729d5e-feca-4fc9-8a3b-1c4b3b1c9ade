version: '3.8'

services:
  # 后端服务 - 测试环境
  backend:
    image: ${CI_REGISTRY_IMAGE}/backend:latest  # 将从CI/CD中替换
    container_name: erp-backend-test
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=test
      - GIN_MODE=debug
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=erp_user
      - DB_PASSWORD=${DB_PASSWORD:-erp_test_password}
      - DB_NAME=erp_test
      - DB_SSL_MODE=disable
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET:-test_jwt_secret_key_for_backend_api_testing}
      - LOG_LEVEL=debug
      - CORS_ALLOWED_ORIGINS=*
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - erp-test-network
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库 - 测试环境
  postgres:
    image: postgres:17-alpine
    container_name: erp-postgres-test
    environment:
      - POSTGRES_DB=erp_test
      - POSTGRES_USER=erp_user
      - POSTGRES_PASSWORD=${DB_PASSWORD:-erp_test_password}
      - POSTGRES_HOST_AUTH_METHOD=md5
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=C
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./backend/migrations/postgres:/docker-entrypoint-initdb.d/
    ports:
      - "5432:5432"  # 避免与原有配置端口冲突
    restart: unless-stopped
    networks:
      - erp-test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_user -d erp_test"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=128MB
      -c effective_cache_size=256MB

  # Redis缓存 - 测试环境
  redis:
    image: redis:7-alpine
    container_name: erp-redis-test
    ports:
      - "6379:6379"  # 避免与原有配置端口冲突
    volumes:
      - redis_test_data:/data
      - ./redis/redis-test.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - erp-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    command: redis-server /usr/local/etc/redis/redis.conf

  # 数据库管理工具 - pgAdmin (测试环境专用)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: erp-pgadmin-test
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_test_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - erp-test-network

  # Redis管理工具 - RedisInsight (测试环境专用)
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: erp-redis-insight-test
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - erp-test-network

  # API文档服务 - Swagger UI (测试环境专用)
  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: erp-swagger-test
    environment:
      - SWAGGER_JSON_URL=http://backend:8080/swagger/doc.json
      - BASE_URL=/swagger-ui
    ports:
      - "8002:8080"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - erp-test-network

  # 日志收集 - 测试环境
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: erp-filebeat-test
    user: root
    volumes:
      - ./logs:/var/log/app:ro
      - ./filebeat/filebeat-test.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - erp-test-network
    command: filebeat -e -strict.perms=false

networks:
  erp-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  pgadmin_test_data:
    driver: local
  redis_insight_data:
    driver: local
