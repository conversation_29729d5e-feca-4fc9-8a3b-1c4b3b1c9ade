# Filebeat测试环境配置

filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/app/*.log
    - /var/log/app/**/*.log
  fields:
    environment: test
    service: erp-backend
  fields_under_root: true
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

- type: container
  enabled: true
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
- add_docker_metadata: ~
- add_kubernetes_metadata: ~

# 输出到控制台 (测试环境)
output.console:
  enabled: true
  pretty: true

# 输出到文件 (测试环境备份)
output.file:
  enabled: true
  path: "/tmp/filebeat"
  filename: filebeat-test
  rotate_every_kb: 10000
  number_of_files: 5

# 日志级别
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# 监控
monitoring.enabled: false
