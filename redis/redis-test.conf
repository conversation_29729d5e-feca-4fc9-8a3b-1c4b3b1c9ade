# Redis测试环境配置文件

# 基础配置
port 6379
bind 0.0.0.0
protected-mode no
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 数据库配置
databases 16

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
maxclients 10000

# 安全配置 (测试环境无密码)
# requirepass your_password

# 键空间通知
notify-keyspace-events Ex

# 测试环境专用配置
# 禁用一些危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_TEST_ONLY"

# 启用调试模式
debug-mode yes
