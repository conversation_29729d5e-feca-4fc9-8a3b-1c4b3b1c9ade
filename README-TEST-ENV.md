# 🧪 ERP后端测试环境部署指南

## 📋 概述

本文档介绍如何快速启动ERP后端测试环境，为前端开发人员提供稳定的API接口调试环境。

## 🎯 测试环境特点

- **专为前端调试设计**: 提供完整的API接口和管理工具
- **开发友好**: 包含数据库管理、Redis监控、API文档等工具
- **测试数据预置**: 自动初始化测试用户、商品、订单等数据
- **日志收集**: 集成Filebeat进行日志收集和分析
- **健康监控**: 完整的服务健康检查机制

## 🚀 快速启动

### 1. 一键启动脚本

```bash
# 执行快速启动脚本
./scripts/test-env-setup.sh
```

### 2. 手动启动

```bash
# 1. 创建环境变量文件
cp .env.test.example .env.test

# 2. 启动服务
docker-compose -f docker-compose.backend-test.yml up -d

# 3. 查看服务状态
docker-compose -f docker-compose.backend-test.yml ps
```

## 🔗 服务访问地址

### 核心服务
| 服务 | 地址 | 说明 |
|------|------|------|
| **后端API** | http://localhost:8080 | 主要API接口 |
| **API文档** | http://localhost:8080/swagger/index.html | Swagger文档 |
| **健康检查** | http://localhost:8080/health | 服务健康状态 |

### 管理工具
| 工具 | 地址 | 用户名/密码 | 说明 |
|------|------|-------------|------|
| **pgAdmin** | http://localhost:5050 | <EMAIL> / admin123 | PostgreSQL管理 |
| **RedisInsight** | http://localhost:8001 | - | Redis监控管理 |
| **Swagger UI** | http://localhost:8002 | - | 独立API文档 |

### 数据库连接
| 数据库 | 地址 | 用户名/密码 | 数据库名 |
|--------|------|-------------|----------|
| **PostgreSQL** | localhost:5434 | erp_user / erp_test_password_2024 | erp_test |
| **Redis** | localhost:6380 | - | - |

## 👤 测试账号

系统预置了以下测试账号：

| 角色 | 邮箱 | 密码 | 权限 |
|------|------|------|------|
| **管理员** | <EMAIL> | password | 全部权限 |
| **普通用户** | <EMAIL> | password | 基础权限 |
| **经理** | <EMAIL> | password | 管理权限 |

## 📊 预置测试数据

### 商品数据
- 测试商品1 (SKU: TEST-001) - 电子产品
- 测试商品2 (SKU: TEST-002) - 服装鞋帽  
- 测试商品3 (SKU: TEST-003) - 家居用品

### 客户数据
- 测试客户A (个人客户)
- 测试客户B (企业客户)
- 测试客户C (个人客户)

### 订单数据
- 待处理订单 (ORD-TEST-001)
- 已确认订单 (ORD-TEST-002)
- 已发货订单 (ORD-TEST-003)

## 🔧 前端调试配置

### 环境变量配置

在前端项目中创建 `.env.test` 文件：

```bash
# 测试环境API地址
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_ENV=test

# 如果部署到服务器
# VITE_API_BASE_URL=http://your-server-ip:8080
```

### API调用示例

```typescript
// 用户登录
const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
});

// 获取商品列表
const productsResponse = await fetch('http://localhost:8080/api/products', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## 🛠️ 常用运维命令

### 服务管理
```bash
# 查看服务状态
docker-compose -f docker-compose.backend-test.yml ps

# 查看所有日志
docker-compose -f docker-compose.backend-test.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.backend-test.yml logs -f backend
docker-compose -f docker-compose.backend-test.yml logs -f postgres

# 重启服务
docker-compose -f docker-compose.backend-test.yml restart backend

# 停止所有服务
docker-compose -f docker-compose.backend-test.yml down

# 清理数据重新开始
docker-compose -f docker-compose.backend-test.yml down -v
```

### 数据库操作
```bash
# 连接PostgreSQL
docker-compose -f docker-compose.backend-test.yml exec postgres psql -U erp_user -d erp_test

# 备份数据库
docker-compose -f docker-compose.backend-test.yml exec postgres pg_dump -U erp_user erp_test > backup.sql

# 恢复数据库
docker-compose -f docker-compose.backend-test.yml exec -T postgres psql -U erp_user -d erp_test < backup.sql
```

### Redis操作
```bash
# 连接Redis
docker-compose -f docker-compose.backend-test.yml exec redis redis-cli

# 查看Redis信息
docker-compose -f docker-compose.backend-test.yml exec redis redis-cli info

# 清空Redis缓存
docker-compose -f docker-compose.backend-test.yml exec redis redis-cli flushall
```

## 🔍 故障排查

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8080
   lsof -i :5432
   lsof -i :6379
   
   # 修改docker-compose.yml中的端口映射
   ```

2. **服务启动失败**
   ```bash
   # 查看详细错误日志
   docker-compose -f docker-compose.backend-test.yml logs backend
   
   # 检查容器状态
   docker ps -a
   ```

3. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   docker-compose -f docker-compose.backend-test.yml exec postgres pg_isready -U erp_user
   
   # 重启数据库服务
   docker-compose -f docker-compose.backend-test.yml restart postgres
   ```

4. **API无响应**
   ```bash
   # 检查后端服务健康状态
   curl http://localhost:8080/health
   
   # 查看后端服务日志
   docker-compose -f docker-compose.backend-test.yml logs -f backend
   ```

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看服务健康检查状态
docker-compose -f docker-compose.backend-test.yml ps
```

## 📝 开发工作流

### 1. 本地开发流程
```bash
# 1. 启动测试环境
./scripts/test-env-setup.sh

# 2. 前端连接测试环境API
# 修改前端.env.test文件指向localhost:8080

# 3. 开发调试
# 前端可以直接调用测试环境的API接口
```

### 2. CI/CD部署流程
```bash
# 1. 提交代码到test分支
git checkout test
git merge develop
git push origin test

# 2. GitLab自动构建部署
# CI/CD会自动构建后端镜像并部署到测试环境

# 3. 前端调试
# 前端团队可以使用部署后的API进行调试
```

## 🎯 最佳实践

### 1. 数据管理
- 定期备份测试数据
- 使用脚本重置测试数据
- 避免在测试环境存储敏感数据

### 2. 日志管理
- 定期清理日志文件
- 使用日志级别控制输出
- 监控错误日志

### 3. 性能优化
- 定期清理Docker镜像
- 监控容器资源使用
- 优化数据库查询

## 🔒 安全注意事项

- 测试环境仅供内部使用
- 不要在测试环境使用生产数据
- 定期更新测试环境密码
- 限制测试环境网络访问

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排查部分
2. 检查服务日志获取详细错误信息
3. 联系DevOps团队获取支持

---

**最后更新**: 2024-07-04  
**维护团队**: DevOps & Backend Team
